// components/Agent.js

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const getClothingAdvice = (temp, condition) => {
  if (temp > 25) return "Bugün hava sıcak! Tişört, şort ve güneş gözlüğü uygun.";
  if (temp > 15) return "<PERSON>ık bir hava var. İnce ceket ve pantolon önerilir.";
  if (temp > 5) return "Hava serin. Mont, bot ve şemsiye almalısın.";
  return "Çok soğuk! Kalın mont, atkı, bere ve eldiven giymelisin.";
};

export default function Agent({ temperature, condition }) {
  const advice = getClothingAdvice(temperature, condition);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>👕 Kıyafet Önerisi</Text>
      <Text style={styles.text}>{advice}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#f1f8e9',
    borderRadius: 10,
    borderColor: '#33691e',
    borderWidth: 1
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6
  },
  text: {
    fontSize: 16
  }
});
