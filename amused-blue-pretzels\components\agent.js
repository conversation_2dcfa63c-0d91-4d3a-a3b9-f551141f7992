// components/Agent.js

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const getClothingAdvice = (temp, condition) => {
  let baseAdvice = "";
  let weatherAdvice = "";

  // Sıcaklığa göre temel kıyafet önerisi
  if (temp > 25) baseAdvice = "Bugün hava sıcak! Tişört, şort ve güneş gözlüğü uygun.";
  else if (temp > 15) baseAdvice = "Ilık bir hava var. İnce ceket ve pantolon önerilir.";
  else if (temp > 5) baseAdvice = "Hava serin. Mont, bot ve şemsiye almalısın.";
  else baseAdvice = "Çok soğuk! Kalın mont, atkı, bere ve eldiven giymelisin.";

  // Hava durumuna göre ek öneriler
  if (condition === 'Rain' || condition === 'Drizzle') {
    weatherAdvice = " ☔ Yağmur var, şemsiye almayı unutma!";
  } else if (condition === 'Snow') {
    weatherAdvice = " ❄️ Kar yağıyor, kaygan yollara dikkat et!";
  } else if (condition === 'Clear') {
    weatherAdvice = " ☀️ Hava açık, güneş gözlüğü faydalı olur!";
  } else if (condition === 'Clouds') {
    weatherAdvice = " ☁️ Bulutlu bir gün, hafif bir ceket yeterli.";
  }

  return baseAdvice + weatherAdvice;
};

export default function Agent({ temperature, condition }) {
  const advice = getClothingAdvice(temperature, condition);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>👕 Kıyafet Önerisi</Text>
      <Text style={styles.text}>{advice}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#f1f8e9',
    borderRadius: 10,
    borderColor: '#33691e',
    borderWidth: 1
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6
  },
  text: {
    fontSize: 16
  }
});
