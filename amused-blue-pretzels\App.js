import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, TextInput, Button, ActivityIndicator } from 'react-native';
import axios from 'axios';
import Agent from './components/agent';// ✅ Agent bileşenini ekledik

export default function App() {
  const [sehir, setSehir] = useState('Istanbul');
  const [havaDurumu, setHavaDurumu] = useState(null);
  const [loading, setLoading] = useState(false);

  const API_KEY = 'f0161cce0b6635d4279d9d7d52ebe35d';

  const veriGetir = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `https://api.openweathermap.org/data/2.5/weather?q=${sehir}&appid=${API_KEY}&units=metric&lang=tr`
      );
      setHavaDurumu(response.data);
    } catch (error) {
      console.log(error);
      alert('Veri alınamadı. Şehir ismini kontrol et.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    veriGetir(); // ilk açıldığında veri getir
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Hava Durumu</Text>
      <TextInput
        style={styles.input}
        placeholder="Şehir girin"
        value={sehir}
        onChangeText={setSehir}
      />
      <Button title="Hava Durumunu Getir" onPress={veriGetir} />
      {loading && <ActivityIndicator size="large" color="#0000ff" />}
      {havaDurumu && (
        <View style={styles.result}>
          <Text style={styles.info}>{havaDurumu.name} - {havaDurumu.weather[0].description}</Text>
          <Text style={styles.info}>Sıcaklık: {havaDurumu.main.temp}°C</Text>
          <Text style={styles.info}>Nem: {havaDurumu.main.humidity}%</Text>
          <Text style={styles.info}>Rüzgar: {havaDurumu.wind.speed} m/s</Text>

          {/* ✅ Agent bileşeni */}
          <Agent
            temperature={havaDurumu.main.temp}
            condition={havaDurumu.weather[0].main}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#e0f7fa',
  },
  title: {
    fontSize: 28,
    marginBottom: 20,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#00796b',
    padding: 10,
    marginBottom: 10,
    borderRadius: 8,
    backgroundColor: '#fff'
  },
  result: {
    marginTop: 20,
    alignItems: 'center',
  },
  info: {
    fontSize: 18,
    marginVertical: 4,
  },
});
